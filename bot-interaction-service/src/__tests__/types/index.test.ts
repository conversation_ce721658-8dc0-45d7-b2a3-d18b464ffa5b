/**
 * Types Index Tests
 */

import * as types from "../../types/index";

describe("Types Index", () => {
  it("should export all type modules", () => {
    expect(types).toBeDefined();
    expect(typeof types).toBe("object");
  });

  it("should have debugger types", () => {
    expect(types.DebuggerEventType).toBeDefined();
    expect(types.LogType).toBeDefined();
  });

  it("should have enum types", () => {
    expect(types.FlowNode).toBeDefined();
    expect(types.MessageNodeType).toBeDefined();
    expect(types.FormNodeType).toBeDefined();
    expect(types.FeedbackType).toBeDefined();
    expect(types.NotificationChannel).toBeDefined();
    expect(types.ChannelSupport).toBeDefined();
  });

  it("should export MessageNodeType enum values", () => {
    expect(types.MessageNodeType.TEXT).toBe("text");
    expect(types.MessageNodeType.IMAGE).toBe("image");
  });

  it("should export FlowNode enum values", () => {
    expect(types.FlowNode.MESSAGE).toBe("message");
    expect(types.FlowNode.FORM).toBe("form");
    expect(types.FlowNode.FLOW_CONNECTOR).toBe("flowConnector");
    expect(types.FlowNode.AGENT_TRANSFER).toBe("agentTransfer");
    expect(types.FlowNode.FEEDBACK).toBe("feedback");
    expect(types.FlowNode.NOTIFICATION).toBe("notification");
  });

  it("should export FormNodeType enum values", () => {
    expect(types.FormNodeType.SINGLE_ASK_FORM).toBe("single-ask-form");
    expect(types.FormNodeType.MULTI_ASK_FORM).toBe("multi-ask-form");
  });

  it("should export DebuggerEventType enum values", () => {
    expect(types.DebuggerEventType.LOG).toBe("log");
    expect(types.DebuggerEventType.NLU_LOG).toBe("nlu_log");
    expect(types.DebuggerEventType.CONTEXT).toBe("context");
  });

  it("should export LogType enum values", () => {
    expect(types.LogType.INFO).toBe("info");
    expect(types.LogType.WARNING).toBe("warning");
    expect(types.LogType.ERROR).toBe("error");
  });

  it("should export FeedbackType enum values", () => {
    expect(types.FeedbackType.STAR_RATING).toBe("star_rating");
    expect(types.FeedbackType.THUMBS_UP).toBe("thumbs_up");
    expect(types.FeedbackType.TEXT).toBe("text");
  });

  it("should export NotificationChannel enum values", () => {
    expect(types.NotificationChannel.SMS).toBe("sms");
    expect(types.NotificationChannel.EMAIL).toBe("email");
    expect(types.NotificationChannel.BOTH).toBe("both");
  });

  it("should export ChannelSupport enum values", () => {
    expect(types.ChannelSupport.WEB).toBe("web");
    expect(types.ChannelSupport.MOBILE).toBe("mobile");
    expect(types.ChannelSupport.WHATSAPP).toBe("whatsapp");
    expect(types.ChannelSupport.SMS).toBe("sms");
    expect(types.ChannelSupport.ALL).toBe("all");
  });

  it("should have all enum values as strings", () => {
    // Check MessageNodeType values are strings
    Object.values(types.MessageNodeType).forEach((value) => {
      expect(typeof value).toBe("string");
    });

    // Check FlowNode values are strings
    Object.values(types.FlowNode).forEach((value) => {
      expect(typeof value).toBe("string");
    });

    // Check DebuggerEventType values are strings
    Object.values(types.DebuggerEventType).forEach((value) => {
      expect(typeof value).toBe("string");
    });

    // Check LogType values are strings
    Object.values(types.LogType).forEach((value) => {
      expect(typeof value).toBe("string");
    });

    // Check FeedbackType values are strings
    Object.values(types.FeedbackType).forEach((value) => {
      expect(typeof value).toBe("string");
    });

    // Check NotificationChannel values are strings
    Object.values(types.NotificationChannel).forEach((value) => {
      expect(typeof value).toBe("string");
    });

    // Check ChannelSupport values are strings
    Object.values(types.ChannelSupport).forEach((value) => {
      expect(typeof value).toBe("string");
    });
  });

  it("should have unique enum values within each enum", () => {
    // Check MessageNodeType has unique values
    const messageNodeTypeValues = Object.values(types.MessageNodeType);
    const uniqueMessageNodeTypeValues = [...new Set(messageNodeTypeValues)];
    expect(messageNodeTypeValues.length).toBe(uniqueMessageNodeTypeValues.length);

    // Check FlowNode has unique values
    const flowNodeValues = Object.values(types.FlowNode);
    const uniqueFlowNodeValues = [...new Set(flowNodeValues)];
    expect(flowNodeValues.length).toBe(uniqueFlowNodeValues.length);

    // Check DebuggerEventType has unique values
    const debuggerEventTypeValues = Object.values(types.DebuggerEventType);
    const uniqueDebuggerEventTypeValues = [...new Set(debuggerEventTypeValues)];
    expect(debuggerEventTypeValues.length).toBe(uniqueDebuggerEventTypeValues.length);

    // Check LogType has unique values
    const logTypeValues = Object.values(types.LogType);
    const uniqueLogTypeValues = [...new Set(logTypeValues)];
    expect(logTypeValues.length).toBe(uniqueLogTypeValues.length);

    // Check FeedbackType has unique values
    const feedbackTypeValues = Object.values(types.FeedbackType);
    const uniqueFeedbackTypeValues = [...new Set(feedbackTypeValues)];
    expect(feedbackTypeValues.length).toBe(uniqueFeedbackTypeValues.length);

    // Check NotificationChannel has unique values
    const notificationChannelValues = Object.values(types.NotificationChannel);
    const uniqueNotificationChannelValues = [...new Set(notificationChannelValues)];
    expect(notificationChannelValues.length).toBe(uniqueNotificationChannelValues.length);

    // Check ChannelSupport has unique values
    const channelSupportValues = Object.values(types.ChannelSupport);
    const uniqueChannelSupportValues = [...new Set(channelSupportValues)];
    expect(channelSupportValues.length).toBe(uniqueChannelSupportValues.length);
  });
});
