/**
 * Configuration Tests
 */

// Mock environment variables before importing config
const originalEnv = process.env;

beforeEach(() => {
  jest.resetModules();
  process.env = { ...originalEnv };
});

afterEach(() => {
  process.env = originalEnv;
});

describe("Configuration", () => {
  it("should load default configuration", () => {
    // Set minimal required env vars
    process.env.DATABASE_URL = "postgresql://test:test@localhost:5432/test_db";
    process.env.REDIS_URL = "redis://localhost:6379";
    process.env.JWT_SECRET = "test-secret";
    process.env.API_KEY = "test-api-key";
    process.env.RASA_NLU_URL = "http://localhost:5005";
    process.env.BOT_BUILDER_URL = "http://localhost:3000";
    process.env.CHAT_SERVICE_URL = "http://localhost:3002";

    const config = require("../../config").default;

    expect(config).toBeDefined();
    expect(config.server).toBeDefined();
    expect(config.database).toBeDefined();
    expect(config.redis).toBeDefined();
    expect(config.services).toBeDefined();
    expect(config.security).toBeDefined();
    expect(config.session).toBeDefined();
    expect(config.logging).toBeDefined();
  });

  it("should use environment variables when provided", () => {
    process.env.NODE_ENV = "production";
    process.env.PORT = "4000";
    process.env.DATABASE_URL = "*************************************/prod_db";
    process.env.REDIS_URL = "redis://prod-redis:6379";
    process.env.JWT_SECRET = "prod-secret";
    process.env.API_KEY = "prod-api-key";
    process.env.RASA_NLU_URL = "http://prod-rasa:5005";
    process.env.BOT_BUILDER_URL = "http://prod-builder:3000";
    process.env.CHAT_SERVICE_URL = "http://prod-chat:3002";
    process.env.CORS_ORIGINS = "https://prod.example.com,https://app.example.com";

    const config = require("../../config").default;

    expect(config.server.env).toBe("production");
    expect(config.server.port).toBe(4000);
    expect(config.database.url).toBe("*************************************/prod_db");
    expect(config.redis.url).toBe("redis://prod-redis:6379");
    expect(config.security.jwtSecret).toBe("prod-secret");
    expect(config.security.apiKey).toBe("prod-api-key");
    expect(config.services.rasaNluUrl).toBe("http://prod-rasa:5005");
    expect(config.services.botBuilderUrl).toBe("http://prod-builder:3000");
    expect(config.services.chatServiceUrl).toBe("http://prod-chat:3002");
    expect(config.server.corsOrigins).toEqual([
      "https://prod.example.com",
      "https://app.example.com",
    ]);
  });

  it("should parse database URL correctly", () => {
    process.env.DATABASE_URL = "********************************/dbname?ssl=true";
    process.env.REDIS_URL = "redis://localhost:6379";
    process.env.JWT_SECRET = "test-secret";
    process.env.API_KEY = "test-api-key";
    process.env.RASA_NLU_URL = "http://localhost:5005";
    process.env.BOT_BUILDER_URL = "http://localhost:3000";
    process.env.CHAT_SERVICE_URL = "http://localhost:3002";

    const config = require("../../config").default;

    expect(config.database.host).toBe("host");
    expect(config.database.port).toBe(5433);
    expect(config.database.name).toBe("dbname");
    expect(config.database.user).toBe("user");
    expect(config.database.password).toBe("pass");
    expect(config.database.ssl).toBe(true);
  });

  it("should parse redis URL correctly", () => {
    process.env.DATABASE_URL = "postgresql://test:test@localhost:5432/test_db";
    process.env.REDIS_URL = "redis://user:pass@redis-host:6380/2";
    process.env.JWT_SECRET = "test-secret";
    process.env.API_KEY = "test-api-key";
    process.env.RASA_NLU_URL = "http://localhost:5005";
    process.env.BOT_BUILDER_URL = "http://localhost:3000";
    process.env.CHAT_SERVICE_URL = "http://localhost:3002";

    const config = require("../../config").default;

    expect(config.redis.host).toBe("redis-host");
    expect(config.redis.port).toBe(6380);
    expect(config.redis.db).toBe(2);
  });

  it("should use default values for optional environment variables", () => {
    process.env.DATABASE_URL = "postgresql://test:test@localhost:5432/test_db";
    process.env.REDIS_URL = "redis://localhost:6379";
    process.env.JWT_SECRET = "test-secret";
    process.env.API_KEY = "test-api-key";
    process.env.RASA_NLU_URL = "http://localhost:5005";
    process.env.BOT_BUILDER_URL = "http://localhost:3000";
    process.env.CHAT_SERVICE_URL = "http://localhost:3002";

    const config = require("../../config").default;

    expect(config.server.port).toBe(3001);
    expect(config.server.env).toBe("development");
    expect(config.session.ttlMinutes).toBe(30);
    expect(config.session.maxConcurrentSessions).toBe(10);
    expect(config.logging.level).toBe("info");
  });

  it("should handle missing required environment variables", () => {
    // Don't set required env vars
    delete process.env.DATABASE_URL;
    delete process.env.REDIS_URL;
    delete process.env.JWT_SECRET;

    // The config might have defaults, so just check it loads
    const config = require("../../config").default;
    expect(config).toBeDefined();
  });

  it("should parse numeric environment variables correctly", () => {
    process.env.DATABASE_URL = "postgresql://test:test@localhost:5432/test_db";
    process.env.REDIS_URL = "redis://localhost:6379";
    process.env.JWT_SECRET = "test-secret";
    process.env.API_KEY = "test-api-key";
    process.env.RASA_NLU_URL = "http://localhost:5005";
    process.env.BOT_BUILDER_URL = "http://localhost:3000";
    process.env.CHAT_SERVICE_URL = "http://localhost:3002";
    process.env.PORT = "8080";
    process.env.SESSION_TTL_MINUTES = "60";
    process.env.MAX_CONCURRENT_SESSIONS = "20";
    process.env.SCRIPT_TIMEOUT_MS = "200";
    process.env.ASYNC_TIMEOUT_MS = "60000";

    const config = require("../../config").default;

    expect(config.server.port).toBe(8080);
    expect(config.session.ttlMinutes).toBe(60);
    expect(config.session.maxConcurrentSessions).toBe(20);
    expect(config.session.scriptTimeoutMs).toBe(200);
    expect(config.session.asyncTimeoutMs).toBe(60000);
  });

  it("should parse boolean environment variables correctly", () => {
    process.env.DATABASE_URL = "postgresql://test:test@localhost:5432/test_db";
    process.env.REDIS_URL = "redis://localhost:6379";
    process.env.JWT_SECRET = "test-secret";
    process.env.API_KEY = "test-api-key";
    process.env.RASA_NLU_URL = "http://localhost:5005";
    process.env.BOT_BUILDER_URL = "http://localhost:3000";
    process.env.CHAT_SERVICE_URL = "http://localhost:3002";
    process.env.DATABASE_SSL = "true";
    process.env.DATABASE_MAX_CONNECTIONS = "50";

    const config = require("../../config").default;

    expect(config.database.ssl).toBe(true);
    expect(config.database.maxConnections).toBe(50);
  });

  it("should handle CORS origins as comma-separated string", () => {
    process.env.DATABASE_URL = "postgresql://test:test@localhost:5432/test_db";
    process.env.REDIS_URL = "redis://localhost:6379";
    process.env.JWT_SECRET = "test-secret";
    process.env.API_KEY = "test-api-key";
    process.env.RASA_NLU_URL = "http://localhost:5005";
    process.env.BOT_BUILDER_URL = "http://localhost:3000";
    process.env.CHAT_SERVICE_URL = "http://localhost:3002";
    process.env.CORS_ORIGINS = "http://localhost:3000,http://localhost:3001,https://example.com";

    const config = require("../../config").default;

    expect(config.server.corsOrigins).toEqual([
      "http://localhost:3000",
      "http://localhost:3001",
      "https://example.com",
    ]);
  });

  it("should handle Redis key prefix", () => {
    process.env.DATABASE_URL = "postgresql://test:test@localhost:5432/test_db";
    process.env.REDIS_URL = "redis://localhost:6379";
    process.env.JWT_SECRET = "test-secret";
    process.env.API_KEY = "test-api-key";
    process.env.RASA_NLU_URL = "http://localhost:5005";
    process.env.BOT_BUILDER_URL = "http://localhost:3000";
    process.env.CHAT_SERVICE_URL = "http://localhost:3002";
    process.env.REDIS_KEY_PREFIX = "myapp:";

    const config = require("../../config").default;

    expect(config.redis.keyPrefix).toBe("myapp:");
  });
});
