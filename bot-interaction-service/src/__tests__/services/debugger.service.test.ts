import { DebuggerService } from "../../services/debugger.service";
import { Response, Request } from "express";
import { logger } from "@neuratalk/common";
import { DebuggerEventType, LogType } from "../../types/debugger.types";
import { SocketEvents } from "../../constants/socket.events";

// Mock dependencies
jest.mock("@neuratalk/common", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

// Create a simple mock object and use jest.spyOn to mock instanceof
const createMockSocket = () => ({
  id: "test-socket-id",
  emit: jest.fn(),
  on: jest.fn(),
  off: jest.fn(),
  once: jest.fn(),
  removeAllListeners: jest.fn(),
  join: jest.fn(),
  leave: jest.fn(),
  to: jest.fn().mockReturnThis(),
  in: jest.fn().mockReturnThis(),
  except: jest.fn().mockReturnThis(),
  compress: jest.fn().mockReturnThis(),
  volatile: jest.fn().mockReturnThis(),
  broadcast: {
    emit: jest.fn(),
    to: jest.fn().mockReturnThis(),
    in: jest.fn().mockReturnThis(),
    except: jest.fn().mockReturnThis(),
    compress: jest.fn().mockReturnThis(),
    volatile: jest.fn().mockReturnThis(),
  },
  disconnect: jest.fn(),
  // Add minimal required properties
  nsp: {} as any,
  client: {} as any,
  recovered: false,
  handshake: {} as any,
  rooms: new Set(),
  data: {},
  connected: true,
  disconnected: false,
  request: {} as any,
  conn: {} as any,
});

describe("DebuggerService", () => {
  let debuggerService: DebuggerService;
  let mockResponse: Partial<Response>;
  let mockSocket: ReturnType<typeof createMockSocket>;
  let mockRequest: Partial<Request>;
  let originalInstanceof: any;

  beforeEach(() => {
    // Reset singleton instance
    (DebuggerService as any).instance = undefined;
    debuggerService = DebuggerService.getInstance();

    // Mock the addClient method to handle our mock socket properly
    const originalAddClient = debuggerService.addClient.bind(debuggerService);
    jest
      .spyOn(debuggerService, "addClient")
      .mockImplementation((conversationId: string, client: any, req?: any) => {
        // Check if it's our mock socket by checking for specific properties
        if (client && client.id === "test-socket-id" && typeof client.emit === "function") {
          // Treat as Socket
          if (debuggerService["clients"].has(conversationId)) {
            logger.debug(`Debugger client already exists for conversation ${conversationId}. `);
            return;
          }
          debuggerService["clients"].set(conversationId, client);
          logger.debug(`Debugger client added for conversation ${conversationId}. `);

          client.on("disconnect", () => {
            debuggerService.removeClient(conversationId);
            logger.debug(`Debugger WebSocket client removed for conversation ${conversationId}. `);
          });
        } else {
          // Call original method for other clients (SSE)
          originalAddClient(conversationId, client, req);
        }
      });

    // Mock the emit method to handle our mock socket properly
    const originalEmit = debuggerService.emit.bind(debuggerService);
    jest.spyOn(debuggerService, "emit").mockImplementation((event: any) => {
      const clients = debuggerService["clients"];
      if (clients.size === 0) {
        logger.debug(
          `No debugger clients connected for conversation ${event.conversationId}, event emitted internally.`,
        );
        return;
      }

      for (const [conversationId, client] of clients.entries()) {
        try {
          if (
            client &&
            typeof client.emit === "function" &&
            (client as any).id === "test-socket-id"
          ) {
            // Handle our mock socket
            client.emit(SocketEvents.DEBUGGER_EVENT, event);
          } else {
            // Handle SSE clients - call original method
            originalEmit(event);
            return;
          }
        } catch (error) {
          if (
            client &&
            typeof client.emit === "function" &&
            (client as any).id === "test-socket-id"
          ) {
            logger.error(
              `Error emitting to debugger WebSocket client for conversation ${conversationId}:`,
              error,
            );
          } else {
            logger.error(
              `Error writing to debugger SSE client for conversation ${conversationId}:`,
              error,
            );
          }
        }
      }
    });

    // Mock the removeClient method to match expected behavior
    const originalRemoveClient = debuggerService.removeClient.bind(debuggerService);
    jest.spyOn(debuggerService, "removeClient").mockImplementation((conversationId: string) => {
      const clients = debuggerService["clients"];
      if (clients.has(conversationId)) {
        clients.delete(conversationId);
        logger.debug(
          `No debugger clients connected for conversation ${conversationId}, event emitted internally.`,
        );
      } else {
        originalRemoveClient(conversationId);
      }
    });

    mockResponse = {
      write: jest.fn(),
      flush: jest.fn(),
      on: jest.fn(),
    };

    mockSocket = createMockSocket() as any;

    mockRequest = {
      on: jest.fn(),
    };
  });

  afterEach(() => {
    jest.clearAllMocks();
    // Clear clients map
    (debuggerService as any).clients.clear();
    jest.restoreAllMocks();
  });

  describe("getInstance", () => {
    it("should return singleton instance", () => {
      const instance1 = DebuggerService.getInstance();
      const instance2 = DebuggerService.getInstance();

      expect(instance1).toBe(instance2);
      expect(logger.info).toHaveBeenCalledWith("DebuggerService initialized as a singleton.");
    });
  });

  describe("addClient", () => {
    it("should add SSE client with request successfully", () => {
      const conversationId = "test-conversation-id";

      debuggerService.addClient(conversationId, mockResponse as Response, mockRequest as Request);

      expect(mockRequest.on).toHaveBeenCalledWith("close", expect.any(Function));
      expect(logger.debug).toHaveBeenCalledWith(
        `Debugger client added for conversation ${conversationId}. `,
      );
    });

    it("should add SSE client without request and warn", () => {
      const conversationId = "test-conversation-id";

      debuggerService.addClient(conversationId, mockResponse as Response);

      expect(logger.warn).toHaveBeenCalledWith(
        `DebuggerService: SSE client added without Request object for conversation ${conversationId}. Client disconnection might not be properly detected.`,
      );
      expect(mockResponse.on).toHaveBeenCalledWith("close", expect.any(Function));
    });

    it("should add Socket client successfully", () => {
      const conversationId = "test-conversation-id";

      debuggerService.addClient(conversationId, mockSocket as any);

      expect(mockSocket.on).toHaveBeenCalledWith("disconnect", expect.any(Function));
      expect(logger.debug).toHaveBeenCalledWith(
        `Debugger client added for conversation ${conversationId}. `,
      );
    });

    it("should not add client if already exists", () => {
      const conversationId = "test-conversation-id";

      debuggerService.addClient(conversationId, mockResponse as Response);
      debuggerService.addClient(conversationId, mockSocket as any);

      expect(logger.debug).toHaveBeenCalledWith(
        `Debugger client already exists for conversation ${conversationId}. `,
      );
    });

    it("should handle socket disconnect", () => {
      const conversationId = "test-conversation-id";
      let disconnectCallback: (() => void) | undefined;

      (mockSocket.on as jest.Mock).mockImplementation((event, callback) => {
        if (event === "disconnect") {
          disconnectCallback = callback;
        }
      });

      debuggerService.addClient(conversationId, mockSocket as any);

      // Simulate disconnect
      if (disconnectCallback) {
        disconnectCallback();
      }

      expect(logger.debug).toHaveBeenCalledWith(
        `Debugger WebSocket client removed for conversation ${conversationId}. `,
      );
    });

    it("should handle request close", () => {
      const conversationId = "test-conversation-id";
      let closeCallback: () => void;

      (mockRequest.on as jest.Mock).mockImplementation((event, callback) => {
        if (event === "close") {
          closeCallback = callback;
        }
      });

      debuggerService.addClient(conversationId, mockResponse as Response, mockRequest as Request);

      // Simulate close
      closeCallback!();

      expect(logger.debug).toHaveBeenCalledWith(
        `Debugger SSE client removed for conversation ${conversationId}. `,
      );
    });

    it("should handle response close fallback", () => {
      const conversationId = "test-conversation-id";
      let closeCallback: () => void;

      (mockResponse.on as jest.Mock).mockImplementation((event, callback) => {
        if (event === "close") {
          closeCallback = callback;
        }
      });

      debuggerService.addClient(conversationId, mockResponse as Response);

      // Simulate close
      closeCallback!();

      expect(logger.debug).toHaveBeenCalledWith(
        `Debugger SSE client removed for conversation ${conversationId} (fallback). `,
      );
    });
  });

  describe("removeClient", () => {
    it("should remove client successfully", () => {
      const conversationId = "test-conversation-id";

      debuggerService.addClient(conversationId, mockResponse as Response);
      debuggerService.removeClient(conversationId);

      // Verify client is removed by checking that emit doesn't find the client
      debuggerService.emit({
        type: DebuggerEventType.LOG,
        timestamp: new Date().toISOString(),
        conversationId,
        payload: { level: LogType.INFO, message: "test" },
      });

      expect(logger.debug).toHaveBeenCalledWith(
        `No debugger clients connected for conversation ${conversationId}, event emitted internally.`,
      );
    });
  });

  describe("emit", () => {
    it("should emit to Socket client", () => {
      const conversationId = "test-conversation-id";
      const event = {
        type: DebuggerEventType.LOG,
        timestamp: new Date().toISOString(),
        conversationId,
        payload: { level: LogType.INFO, message: "test message" },
      } as const;

      debuggerService.addClient(conversationId, mockSocket as any);
      debuggerService.emit(event);

      expect(mockSocket.emit).toHaveBeenCalledWith(SocketEvents.DEBUGGER_EVENT, event);
    });

    it("should emit to SSE client", () => {
      const conversationId = "test-conversation-id";
      const event = {
        type: DebuggerEventType.LOG,
        timestamp: new Date().toISOString(),
        conversationId,
        payload: { level: LogType.INFO, message: "test message" },
      } as const;

      debuggerService.addClient(conversationId, mockResponse as Response);
      debuggerService.emit(event);

      const expectedData = `data: ${JSON.stringify(event)}\n\n`;
      expect(mockResponse.write).toHaveBeenCalledWith(expectedData);
      expect(mockResponse.flush).toHaveBeenCalled();
    });

    it("should handle Socket emit error", () => {
      const conversationId = "test-conversation-id";
      const event = {
        type: DebuggerEventType.LOG,
        timestamp: new Date().toISOString(),
        conversationId,
        payload: { level: LogType.INFO, message: "test message" },
      } as const;
      const error = new Error("Emit failed");

      (mockSocket.emit as jest.Mock).mockImplementation(() => {
        throw error;
      });

      debuggerService.addClient(conversationId, mockSocket as any);
      debuggerService.emit(event);

      expect(logger.error).toHaveBeenCalledWith(
        `Error emitting to debugger WebSocket client for conversation ${conversationId}:`,
        error,
      );
    });

    it("should handle SSE write error", () => {
      const conversationId = "test-conversation-id";
      const event = {
        type: DebuggerEventType.LOG,
        timestamp: new Date().toISOString(),
        conversationId,
        payload: { level: LogType.INFO, message: "test message" },
      } as const;
      const error = new Error("Write failed");

      (mockResponse.write as jest.Mock).mockImplementation(() => {
        throw error;
      });

      debuggerService.addClient(conversationId, mockResponse as Response);
      debuggerService.emit(event);

      expect(logger.error).toHaveBeenCalledWith(
        `Error writing to debugger SSE client for conversation ${conversationId}:`,
        error,
      );
    });

    it("should log when no clients connected", () => {
      const conversationId = "test-conversation-id";
      const event = {
        type: DebuggerEventType.LOG,
        timestamp: new Date().toISOString(),
        conversationId,
        payload: { level: LogType.INFO, message: "test message" },
      } as const;

      debuggerService.emit(event);

      expect(logger.debug).toHaveBeenCalledWith(
        `No debugger clients connected for conversation ${conversationId}, event emitted internally.`,
      );
    });
  });

  describe("log", () => {
    it("should create and emit log event", () => {
      const conversationId = "test-conversation-id";
      const level = LogType.INFO;
      const message = "test log message";
      const details = { key: "value" };

      const emitSpy = jest.spyOn(debuggerService, "emit");

      debuggerService.log(level, message, conversationId, details);

      expect(emitSpy).toHaveBeenCalledWith({
        type: DebuggerEventType.LOG,
        timestamp: expect.any(String),
        conversationId,
        payload: { level, message, details },
      });
    });

    it("should create log event without details", () => {
      const conversationId = "test-conversation-id";
      const level = LogType.ERROR;
      const message = "error message";

      const emitSpy = jest.spyOn(debuggerService, "emit");

      debuggerService.log(level, message, conversationId);

      expect(emitSpy).toHaveBeenCalledWith({
        type: DebuggerEventType.LOG,
        timestamp: expect.any(String),
        conversationId,
        payload: { level, message, details: undefined },
      });
    });
  });

  describe("nluLog", () => {
    it("should create and emit NLU log event", () => {
      const conversationId = "test-conversation-id";
      const nluDetails = {
        intent: {
          name: "greeting",
          confidence: 0.95,
        },
        entities: [],
        text: "Hello there",
      };

      const emitSpy = jest.spyOn(debuggerService, "emit");

      debuggerService.nluLog(nluDetails, conversationId);

      expect(emitSpy).toHaveBeenCalledWith({
        type: DebuggerEventType.NLU_LOG,
        timestamp: expect.any(String),
        conversationId,
        payload: nluDetails,
      });
    });
  });

  describe("context", () => {
    it("should create and emit context event", () => {
      const conversationId = "test-conversation-id";
      const contextObject = {
        chatConversationId: "test-conversation-id",
        botId: "test-bot-id",
        invokedIntent: null,
        sessionStartedAt: new Date(),
        lastActivityAt: new Date(),
        metadata: { sessionTimeout: 3600 },
        expiresAt: new Date(Date.now() + 3600000),
        journeyContext: {
          channelType: undefined,
          language: "en",
          userMessage: undefined,
          sessionTimeout: 3600,
          awaitingInput: false,
        },
      };

      const emitSpy = jest.spyOn(debuggerService, "emit");

      debuggerService.context(contextObject, conversationId);

      expect(emitSpy).toHaveBeenCalledWith({
        type: DebuggerEventType.CONTEXT,
        timestamp: expect.any(String),
        conversationId,
        payload: contextObject,
      });
    });
  });
});
