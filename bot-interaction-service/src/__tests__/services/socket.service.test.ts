import { SocketService } from "../../services/socket.service";
import { Server as HttpServer } from "http";
import { Socket } from "socket.io";
import { logger } from "@neuratalk/common";
import { SocketEvents } from "../../constants/socket.events";

// Mock dependencies
jest.mock("@neuratalk/common", () => ({
  logger: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn(),
  },
}));

jest.mock("socket.io", () => {
  const mockSocket = {
    id: "test-socket-id",
    on: jest.fn(),
    emit: jest.fn(),
    to: jest.fn().mockReturnThis(),
  };

  const mockIo = {
    on: jest.fn(),
    to: jest.fn().mockReturnThis(),
    emit: jest.fn(),
    disconnectSockets: jest.fn(),
    close: jest.fn().mockResolvedValue(undefined),
  };

  return {
    Server: jest.fn().mockImplementation(() => mockIo),
  };
});

describe("SocketService", () => {
  let socketService: SocketService;
  let mockHttpServer: Partial<HttpServer>;
  let mockSocket: Partial<Socket>;

  beforeEach(() => {
    mockHttpServer = {
      listen: jest.fn(),
      close: jest.fn(),
    };

    mockSocket = {
      id: "test-socket-id",
      on: jest.fn(),
      emit: jest.fn(),
      to: jest.fn().mockReturnThis(),
    };

    socketService = new SocketService(mockHttpServer as HttpServer);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("constructor", () => {
    it("should initialize SocketService with HTTP server", () => {
      expect(socketService).toBeDefined();
      expect(socketService.io).toBeDefined();
      expect(logger.info).toHaveBeenCalledWith("SocketService initialized.");
    });

    it("should setup connection handlers", () => {
      expect(socketService.io.on).toHaveBeenCalledWith(
        SocketEvents.CONNECTION,
        expect.any(Function),
      );
    });
  });

  describe("setupConnectionHandlers", () => {
    it("should handle client connection", () => {
      const connectionHandler = (socketService.io.on as jest.Mock).mock.calls.find(
        (call) => call[0] === SocketEvents.CONNECTION,
      )[1];

      connectionHandler(mockSocket);

      expect(logger.info).toHaveBeenCalledWith(`Client connected: ${mockSocket.id}`);
      expect(mockSocket.on).toHaveBeenCalledWith(SocketEvents.DISCONNECT, expect.any(Function));
      expect(mockSocket.on).toHaveBeenCalledWith(SocketEvents.ERROR, expect.any(Function));
    });

    it("should handle client disconnection", () => {
      const connectionHandler = (socketService.io.on as jest.Mock).mock.calls.find(
        (call) => call[0] === SocketEvents.CONNECTION,
      )[1];

      connectionHandler(mockSocket);

      const disconnectHandler = (mockSocket.on as jest.Mock).mock.calls.find(
        (call) => call[0] === SocketEvents.DISCONNECT,
      )[1];

      disconnectHandler();

      expect(logger.info).toHaveBeenCalledWith(`Client disconnected: ${mockSocket.id}`);
    });

    it("should handle socket errors", () => {
      const connectionHandler = (socketService.io.on as jest.Mock).mock.calls.find(
        (call) => call[0] === SocketEvents.CONNECTION,
      )[1];

      connectionHandler(mockSocket);

      const errorHandler = (mockSocket.on as jest.Mock).mock.calls.find(
        (call) => call[0] === SocketEvents.ERROR,
      )[1];

      const testError = new Error("Test socket error");
      errorHandler(testError);

      expect(logger.error).toHaveBeenCalledWith(`Socket error for ${mockSocket.id}:`, testError);
    });
  });

  describe("emit", () => {
    it("should emit message to conversation room", () => {
      const conversationId = "test-conversation-id";
      const event = SocketEvents.BOT_RESPONSE;
      const data = { message: "test message" };

      socketService.emit(conversationId, event, data);

      expect(socketService.io.to).toHaveBeenCalledWith(`conversation:${conversationId}`);
      expect(socketService.io.emit).toHaveBeenCalledWith(event, data);
      expect(logger.debug).toHaveBeenCalledWith(
        `Emitted event "${event}" to room "${conversationId}" with data: ${JSON.stringify(data)}`,
      );
    });

    it("should emit message with different event types", () => {
      const conversationId = "test-conversation-id";
      const event = SocketEvents.BOT_RESPONSE;
      const data = { response: "bot response" };

      socketService.emit(conversationId, event, data);

      expect(socketService.io.to).toHaveBeenCalledWith(`conversation:${conversationId}`);
      expect(socketService.io.emit).toHaveBeenCalledWith(event, data);
    });

    it("should emit message with null data", () => {
      const conversationId = "test-conversation-id";
      const event = SocketEvents.STATUS;
      const data = null;

      socketService.emit(conversationId, event, data);

      expect(socketService.io.to).toHaveBeenCalledWith(`conversation:${conversationId}`);
      expect(socketService.io.emit).toHaveBeenCalledWith(event, data);
      expect(logger.debug).toHaveBeenCalledWith(
        `Emitted event "${event}" to room "${conversationId}" with data: ${JSON.stringify(data)}`,
      );
    });

    it("should emit message with complex data", () => {
      const conversationId = "test-conversation-id";
      const event = SocketEvents.BOT_RESPONSE;
      const data = {
        message: "complex message",
        metadata: {
          timestamp: new Date().toISOString(),
          userId: "user123",
          nested: {
            property: "value",
          },
        },
      };

      socketService.emit(conversationId, event, data);

      expect(socketService.io.to).toHaveBeenCalledWith(`conversation:${conversationId}`);
      expect(socketService.io.emit).toHaveBeenCalledWith(event, data);
    });
  });

  describe("disconnect", () => {
    it("should disconnect all sockets and close server", async () => {
      await socketService.disconnect();

      expect(socketService.io.disconnectSockets).toHaveBeenCalledWith(true);
      expect(socketService.io.close).toHaveBeenCalled();
      expect(logger.info).toHaveBeenCalledWith("Socket.IO server closed.");
    });

    it("should handle disconnect errors gracefully", async () => {
      const error = new Error("Disconnect failed");
      (socketService.io.close as jest.Mock).mockRejectedValue(error);

      await expect(socketService.disconnect()).rejects.toThrow("Disconnect failed");

      // Reset the mock after the test
      (socketService.io.close as jest.Mock).mockResolvedValue(undefined);
    });

    it("should disconnect sockets before closing", async () => {
      // Ensure clean mock state
      (socketService.io.close as jest.Mock).mockResolvedValue(undefined);
      (socketService.io.disconnectSockets as jest.Mock).mockReturnValue(undefined);

      await socketService.disconnect();

      expect(socketService.io.disconnectSockets).toHaveBeenCalledWith(true);
      expect(socketService.io.close).toHaveBeenCalled();
    });
  });

  describe("integration scenarios", () => {
    it("should handle multiple clients connecting and disconnecting", () => {
      const connectionHandler = (socketService.io.on as jest.Mock).mock.calls.find(
        (call) => call[0] === SocketEvents.CONNECTION,
      )[1];

      const mockSocket1 = { ...mockSocket, id: "socket-1" };
      const mockSocket2 = { ...mockSocket, id: "socket-2" };

      connectionHandler(mockSocket1);
      connectionHandler(mockSocket2);

      expect(logger.info).toHaveBeenCalledWith("Client connected: socket-1");
      expect(logger.info).toHaveBeenCalledWith("Client connected: socket-2");
    });

    it("should emit to multiple conversation rooms", () => {
      const conversationId1 = "conversation-1";
      const conversationId2 = "conversation-2";
      const event = SocketEvents.BOT_RESPONSE;
      const data1 = { message: "message 1" };
      const data2 = { message: "message 2" };

      socketService.emit(conversationId1, event, data1);
      socketService.emit(conversationId2, event, data2);

      expect(socketService.io.to).toHaveBeenCalledWith(`conversation:${conversationId1}`);
      expect(socketService.io.to).toHaveBeenCalledWith(`conversation:${conversationId2}`);
      expect(socketService.io.emit).toHaveBeenCalledWith(event, data1);
      expect(socketService.io.emit).toHaveBeenCalledWith(event, data2);
    });
  });

  describe("error handling", () => {
    it("should handle socket.io server creation errors", () => {
      const { Server } = require("socket.io");
      (Server as jest.Mock).mockImplementationOnce(() => {
        throw new Error("Server creation failed");
      });

      expect(() => {
        new SocketService(mockHttpServer as HttpServer);
      }).toThrow("Server creation failed");
    });

    it("should handle emit errors gracefully", () => {
      const conversationId = "test-conversation-id";
      const event = SocketEvents.BOT_RESPONSE;
      const data = { message: "test message" };

      (socketService.io.emit as jest.Mock).mockImplementation(() => {
        throw new Error("Emit failed");
      });

      expect(() => {
        socketService.emit(conversationId, event, data);
      }).toThrow("Emit failed");
    });
  });

  describe("configuration", () => {
    it("should initialize with correct ping timeout", () => {
      const { Server } = require("socket.io");

      expect(Server).toHaveBeenCalledWith(mockHttpServer, {
        pingTimeout: 60000,
      });
    });
  });
});
