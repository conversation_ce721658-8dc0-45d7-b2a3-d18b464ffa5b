import { Router } from "express";
import { LanguageController } from "../controllers/language.controller";
import { BotLanguageController } from "../controllers/bot-language.controller";
import {
  PaginationQuerySchema,
  UuidParamSchema,
  validateBody,
  validateParams,
  validateQuery,
} from "@neuratalk/common";
import {
  CreateLanguageSchema,
  CreateBotLanguageSchema,
  BulkCreateBotLanguageSchema,
  BulkDeleteBotLanguageSchema,
} from "../schemas/bot-language.schemas";
import { BotIdParamSchema } from "../schemas/bot.schemas";
import { AppContext } from "../types/context.types";
import { authorizeMiddleware } from "../middleware/authorize.middleware";
import { PermissionKeys } from "../types";

export function createLanguageRoutes(context: AppContext): Router {
  const router = Router();

  const languageController = new LanguageController(context);
  const botLanguageController = new BotLanguageController(context);
  // Language routes
  router.post(
    "/languages",
    authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]),
    validateBody(CreateLanguageSchema),
    languageController.create
  );
  router.get(
    "/languages",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateQuery(PaginationQuerySchema),
    languageController.getAll,
  );
  router.get(
    "/languages/:id",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateParams(UuidParamSchema),
    languageController.getById
  );

  // Bot Language routes
  router.post(
    "/bot-languages/:botId",
    authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]),
    validateParams(BotIdParamSchema),
    validateBody(CreateBotLanguageSchema),
    botLanguageController.create.bind(botLanguageController),
  );
  router.post(
    "/bot-languages/:botId/bulk",
    authorizeMiddleware([PermissionKeys.CREATE_NEURATALK]),
    validateParams(BotIdParamSchema),
    validateBody(BulkCreateBotLanguageSchema),
    botLanguageController.bulkCreate,
  );
  router.get(
    "/bot-languages",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateQuery(PaginationQuerySchema),
    botLanguageController.getAll,
  );
  router.get(
    "/bot-languages/:botId",
    authorizeMiddleware([PermissionKeys.READ_NEURATALK]),
    validateParams(BotIdParamSchema),
    botLanguageController.getById,
  );
  router.delete(
    "/bot-languages/:botId",
    authorizeMiddleware([PermissionKeys.DELETE_NEURATALK]),
    validateParams(BotIdParamSchema),
    botLanguageController.delete,
  );
  router.delete(
    "/bot-languages/:botId/bulk",
    authorizeMiddleware([PermissionKeys.DELETE_NEURATALK]),
    validateParams(BotIdParamSchema),
    validateBody(BulkDeleteBotLanguageSchema),
    botLanguageController.bulkDelete,
  );

  return router;
}
